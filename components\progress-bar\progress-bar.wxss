.step-progress-container {
  padding: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 进度条样式 */
.progress-bar-container {
  margin-bottom: 20rpx;
}

.progress-bar-bg {
  width: 100%;
  background-color: #F5F5F5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar-active {
  background-color: #79AA6B;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.step-progress-items {
  display: flex;
  justify-content: center;
}

.step-item {
  display: flex;
  align-items: center;
}

.step-line {
  width: 112rpx;
  height: 8rpx;
  background: #D9D9D9;
  border-radius: 4rpx;
}

.step-item.active .step-line {
  background: #79AA6B;
}

/* 设置步骤之间的间距为28rpx */
.step-item:not(:last-child) {
  margin-right: 28rpx;
}

/* 进度计数器右对齐 */
.progress-counter {
  text-align: right;
  margin-top: 10rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #424242;
  padding: 0 17%;
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #333333;
}


