import { getBaseUrl } from '../../config/env';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 上传按钮文字
    buttonText: {
      type: String,
      value: '拍照识别菜品'
    },
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 样式类型：'default' 默认样式, 'simple' 简约线框样式
    styleType: {
      type: String,
      value: 'simple'
    },
    // 线框宽度（rpx）
    frameWidth: {
      type: Number,
      value: 108
    },
    // 线框高度（rpx）
    frameHeight: {
      type: Number,
      value: 108
    },
    // 边框宽度（rpx）
    borderWidth: {
      type: Number,
      value: 2
    },
    // 边框样式：'solid' 实线, 'dashed' 虚线, 'dotted' 点线
    borderStyle: {
      type: String,
      value: 'solid'
    },
    // 边框颜色
    borderColor: {
      type: String,
      value: '#C4C4C4'
    },
    // 激活时的边框颜色
    activeBorderColor: {
      type: String,
      value: '#7EB67D'
    },
    // 圆角大小（rpx）
    borderRadius: {
      type: Number,
      value: 16
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isRecognizing: false,
    selectedImage: null,
    uploadedImageUrl: null // 上传成功后的图片URL
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 上传文件到服务器
     */
    uploadFile(filePath) {
      return new Promise((resolve, reject) => {
        const baseUrl = getBaseUrl();
        wx.uploadFile({
          url: `${baseUrl}/api/upload`,
          filePath,
          name: 'file',
          formData: {},
          success: (res) => {
            if (res.statusCode === 200) {
              try {
                const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                console.log(data, 'API返回的完整数据');
                
                if (data && data.data && data.data.external_url) {
                  console.log(data.data.external_url, '获取到的图片URL');
                  resolve(data.data.external_url);
                } else {
                  console.log('未找到图片URL，返回完整数据');
                  resolve(data);
                }
              } catch (error) {
                console.error('解析响应数据出错', error);
                reject(error);
              }
            } else {
              console.log('请求状态码异常:', res.statusCode);
              reject(res);
            }
          },
          fail: (err) => {
            console.log('上传请求失败:', err);
            reject(err);
          }
        });
      });
    },

    /**
     * 识别菜品
     */
    recognizeFood(data) {
      return new Promise((resolve, reject) => {
        const baseUrl = getBaseUrl();
        wx.request({
          url: `${baseUrl}/api/ai/food/recognize`,
          method: 'POST',
          data: data,
          header: {
            'content-type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(res);
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },
    /**
     * 点击识别按钮
     */
    async onRecognizeClick() {
      if (this.data.disabled || this.data.isRecognizing) {
        return;
      }

      try {
        // 选择图片
        const imagePath = await this.chooseImage();
        if (!imagePath) return;

        this.setData({
          isRecognizing: true,
          selectedImage: imagePath
        });

        if (this.data.showLoading) {
          wx.showLoading({
            title: '正在识别中...',
            mask: true
          });
        }

        // 触发开始识别事件
        this.triggerEvent('recognizestart', {
          imagePath: imagePath
        });

        // 上传图片
        const imageUrl = await this.uploadFile(imagePath);
        console.log('图片上传成功：', imageUrl);

        // 保存上传后的图片URL，用于显示
        this.setData({
          uploadedImageUrl: imageUrl
        });

        // 识别菜品
        const result = await this.recognizeFood({ image_url: imageUrl });
        console.log('识别结果：', result);

        if (this.data.showLoading) {
          wx.hideLoading();
        }

        // 触发识别成功事件
        this.triggerEvent('recognizesuccess', {
          result: result.data || result,
          imagePath: imagePath,
          imageUrl: imageUrl
        });

        wx.showToast({
          title: '识别成功',
          icon: 'success'
        });

      } catch (error) {
        console.error('识别失败：', error);
        
        if (this.data.showLoading) {
          wx.hideLoading();
        }

        // 触发识别失败事件
        this.triggerEvent('recognizefail', {
          error: error,
          imagePath: this.data.selectedImage
        });

        wx.showToast({
          title: '识别失败，请重试',
          icon: 'none'
        });
      } finally {
        this.setData({
          isRecognizing: false
        });
      }
    },

    /**
     * 选择图片
     */
    chooseImage() {
      return new Promise((resolve, reject) => {
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            resolve(tempFilePath);
          },
          fail: (error) => {
            console.error('选择图片失败：', error);
            resolve(null);
          }
        });
      });
    },

    /**
     * 清除已上传的图片
     */
    clearImage() {
      this.setData({
        uploadedImageUrl: null,
        selectedImage: null
      });
      
      // 触发清除图片事件
      this.triggerEvent('imagecleared');
    }
  }
}); 