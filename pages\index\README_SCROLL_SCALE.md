# 功能导航区域滚动缩放效果

## 功能描述
为功能导航区域的6个图标添加了滚动时的放大效果，实现近大远小的交互动画。当用户滚动页面时，距离屏幕中心越近的图标会越大，距离越远的图标会越小。

## 实现原理

### 1. 滚动监听
- 使用 `onPageScroll` 方法监听页面滚动事件
- 通过节流处理避免频繁计算，提升性能

### 2. 位置计算
- 获取功能导航区域的位置信息 (`functionNavRect`)
- 计算每个图标在屏幕中的实际位置
- 计算图标与屏幕中心的距离

### 3. 缩放计算
- 根据距离屏幕中心的远近计算缩放比例
- 使用平滑的缩放曲线 (`Math.pow(normalizedDistance, 0.8)`)
- 缩放范围：0.7倍 到 1.4倍

## 配置参数

在 `CONFIG.SCROLL_SCALE` 中可以调整以下参数：

```javascript
SCROLL_SCALE: {
  DEBUG: false,        // 调试模式开关
  MAX_SCALE: 1.4,      // 最大缩放倍数
  MIN_SCALE: 0.7,      // 最小缩放倍数
  INFLUENCE_RANGE: 1.2 // 影响范围系数
}
```

## 关键文件修改

### 1. index.js
- 添加滚动监听方法 `onPageScroll`
- 修改 `calculateMenuPositions` 方法，增加缩放计算
- 添加 `initScrollScaleEffect` 初始化方法
- 添加节流处理和定时器清理

### 2. index.wxml
- 修改菜单项的 `transform` 样式，添加 `scale({{item.scale || 1}})`
- 将 `scroll-view` 改为普通 `view`，使用页面滚动

### 3. index.wxss
- 优化菜单项的过渡动画，使用 `cubic-bezier` 缓动函数
- 调整过渡时间为 0.15s，提供平滑的缩放效果

## 性能优化

1. **节流处理**: 滚动事件使用16ms节流，约60fps
2. **硬件加速**: 使用 `will-change: transform` 启用GPU加速
3. **延迟初始化**: 延迟获取DOM位置信息，确保渲染完成
4. **定时器清理**: 在页面隐藏和卸载时清理定时器

## 调试模式

设置 `CONFIG.SCROLL_SCALE.DEBUG = true` 可以在控制台查看调试信息：
- 图标在屏幕中的Y坐标
- 距离屏幕中心的距离
- 当前的缩放比例

## 注意事项

1. 确保功能导航区域已完全渲染后再获取位置信息
2. 滚动性能优化通过节流处理实现
3. 缩放效果不会影响图标的点击区域和功能
4. 兼容原有的旋转和点击动画效果
