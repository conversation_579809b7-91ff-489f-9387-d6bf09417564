/**
 * 进度条组件
 * 
 * 功能：
 * 1. 展示进度百分比
 * 2. 可自定义高度
 * 3. 支持步骤点击事件
 */
Component({
  properties: {
    // 进度百分比，0-100
    progress: {
      type: Number,
      value: 0,
      observer: function(newVal) {
        // 确保进度值在0-100之间
        if (newVal < 0 || newVal > 100) {
          this.setData({
            progress: Math.max(0, Math.min(100, newVal))
          });
        }
      }
    },
    // 进度条高度
    height: {
      type: Number,
      value: 6
    }
  },
  
  data: {},
  
  observers: {},
  
  methods: {
    // 步骤点击事件
    stepTap: function(e) {
      const index = e.currentTarget.dataset.index;
      if (typeof index !== 'undefined') {
        // 触发步骤变化事件
        this.triggerEvent('stepchange', { step: index });
      }
    }
  }
}); 