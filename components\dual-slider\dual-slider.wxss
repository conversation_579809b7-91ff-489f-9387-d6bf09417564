.range-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.slider-container {
  position: relative;
  height: 50px;
  margin: 0 15px;
}

.track {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 3px;
  background: #eee;
  transform: translateY(-50%);
}

.active-track {
  position: absolute;
  top: 50%;
  height: 3px;
  background: #79AA6B;
  transform: translateY(-50%);
}

.thumb {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #fff;
  border: 1px solid #79AA6B;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.thumb:before,.thumb:after{
  margin:0 6rpx;
  width: 2px;
  height: 22rpx;
  content: "";
  background:#d9d9d9;
}

.thumb-label {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: #07C160;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.range-display {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  color: #333;
}

/* v1.2 */
.dual-slider-container {  
  margin: 0 auto;
  width: 91%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.limit-text {
  font-size: 14px;
  color: #666;
  margin: 5px 0;
}

.slider-area {
  width: 100%;
  position: relative;
  height: 50px;
  margin: 10rpx 0;
}