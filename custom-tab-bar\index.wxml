<!--miniprogram/custom-tab-bar/index.wxml-->
<view class="tab-bar" wx:if="{{show}}">
  <view class="tab-bar-border"></view>
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <image src="{{selected === index ? item.selectedIconPath : item.iconPath}}"></image>
    <view class="{{selected === index ? 'text-selected' : 'text-normal'}}">{{item.text}}</view>
  </view>
</view>