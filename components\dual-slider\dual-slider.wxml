<view class="dual-slider-container">
  <!-- 顶部显示最小值 -->
  <!-- <view class="limit-text">{{minText}}</view> -->
  
  <!-- 滑动区域 -->
  <view class="slider-area">
    <view class="track">
      <view class="active-track" style="left: {{leftPercent}}%; width: {{rightPercent - leftPercent}}%;"></view>
    </view>
    <view class="thumb left-thumb" style="left: {{leftPercent}}%;" bindtouchstart="handleLeftTouchStart" bindtouchmove="handleLeftTouchMove">
      <!-- <view class="thumb-value">{{leftValue}}</view> -->
    </view>
    <view class="thumb right-thumb" style="left: {{rightPercent}}%;" bindtouchstart="handleRightTouchStart" bindtouchmove="handleRightTouchMove">
      <!-- <view class="thumb-value">{{rightValue}}</view> -->
    </view>
  </view>
  
  <!-- 底部显示最大值 -->
  <!-- <view class="limit-text">{{maxText}}</view> -->
  
  <!-- 重置按钮 -->
  <!-- <button class="reset-btn" bindtap="handleReset">重置</button> -->
</view>