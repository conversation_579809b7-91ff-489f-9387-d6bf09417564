Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: '餐参Ai'
    },
    showBack: {
      type: Boolean,
      value: false
    },
    showLogo: {
      type: Boolean,
      value: true
    },
    backgroundColor: {
      type: String,
      value: '#ffffff'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    headerStyle: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化头部样式
    initHeaderStyle() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        const statusBarHeight = systemInfo.statusBarHeight;
        const headerHeight = statusBarHeight + 44; // 状态栏高度 + 导航栏高度
        
        // 移除backgroundColor参数，因为现在使用CSS渐变背景
        const headerStyle = `height: ${headerHeight}px; padding-top: ${statusBarHeight}px;`;
        
        this.setData({
          headerStyle: headerStyle
        });

        // 触发事件，通知父组件头部高度
        this.triggerEvent('headerinit', {
          statusBarHeight: statusBarHeight,
          headerHeight: headerHeight
        });
        
      } catch (e) {
        console.error('获取系统信息失败:', e);
        // 使用默认值
        const headerStyle = `height: 64px; padding-top: 20px;`;
        this.setData({
          headerStyle: headerStyle
        });
        
        this.triggerEvent('headerinit', {
          statusBarHeight: 20,
          headerHeight: 64
        });
      }
    },

    // 返回按钮点击事件
    onBackTap() {
      this.triggerEvent('back');
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initHeaderStyle();
    }
  }
}) 