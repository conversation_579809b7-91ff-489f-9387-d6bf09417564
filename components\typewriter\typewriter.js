/**
 * 流式打字机组件
 * 支持实时显示流式响应内容，可选择是否启用打字机效果
 */
import { getBaseUrl } from '@/config/env'

// 兼容的文本解码器实现
const CompatibleTextDecoder = {
  decode: function(arrayBuffer) {
    if (!arrayBuffer) return '';
    const uint8Array = new Uint8Array(arrayBuffer);
    let result = '';
    for (let i = 0; i < uint8Array.length; i++) {
      result += String.fromCharCode(uint8Array[i]);
    }
    return decodeURIComponent(escape(result));
  }
};

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // API请求地址（相对路径，会自动拼接baseUrl）
    url: {
      type: String,
      value: ''
    },
    // 请求参数
    requestData: {
      type: Object,
      value: {}
    },
    // 请求方法
    method: {
      type: String,
      value: 'POST'
    },
    // 请求头
    header: {
      type: Object,
      value: {
        'content-type': 'application/json'
      }
    },
    // 打字速度（毫秒）
    speed: {
      type: Number,
      value: 100,
      observer: function(newVal) {
        // 确保速度为正数
        if (newVal <= 0) {
          this.setData({ speed: 100 });
        }
      }
    },
    // 是否自动开始请求
    autoStart: {
      type: Boolean,
      value: true
    },
    // 是否启用打字机效果
    enableTypewriter: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的内部数据
   */
  data: {
    currentText: '', // 当前显示的文本
    pendingText: '', // 待显示的文本队列（仅打字机模式使用）
    isTyping: false, // 是否正在打字
    isStreaming: false, // 是否正在接收数据流
    timer: null, // 打字定时器
    isPaused: false, // 是否暂停打字
    requestTask: null, // 当前请求任务
    decoder: null, // 文本解码器
    _headerReceivedListener: null,
    _chunkReceivedListener: null
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      try {
        // 初始化文本解码器，优先使用原生 TextDecoder，不支持时使用兼容实现
        this.data.decoder = typeof TextDecoder !== 'undefined' 
          ? new TextDecoder('utf-8')
          : CompatibleTextDecoder;
        
        // 如果设置了自动开始且有URL，则开始请求
        if (this.data.autoStart && this.properties.url) {
          this.startRequest();
        }
      } catch (error) {
        console.error('组件初始化失败:', error);
        this.triggerEvent('error', { error });
      }
    },

    detached() {
      this._cleanup();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 开始流式请求
     */
    startRequest() {
      // 防止重复请求
      if (this.data.isStreaming) {
        console.warn('已有请求正在进行中');
        return;
      }

      // 验证URL
      if (!this.properties.url) {
        console.error('URL未设置');
        this.triggerEvent('requestFail', { error: 'URL is required' });
        return;
      }

      // 重置状态
      this._resetState();

      try {
        const requestTask = wx.request({
          url: getBaseUrl() + this.properties.url,
          method: this.properties.method,
          responseType: 'arraybuffer',
          enableChunked: true,
          header: this.properties.header,
          data: this.properties.requestData,
          success: this._handleRequestSuccess.bind(this),
          fail: this._handleRequestFail.bind(this),
          complete: this._handleRequestComplete.bind(this)
        });

        // 验证流式传输支持
        if (!requestTask || typeof requestTask.onChunkReceived !== 'function') {
          throw new Error('当前环境不支持流式传输');
        }

        // 保存请求任务实例
        this.setData({ 
          requestTask,
          isStreaming: true 
        });

        // 设置数据流监听器
        this._setupStreamListeners(requestTask);

      } catch (error) {
        this._handleError('创建请求失败', error);
      }
    },

    /**
     * 中止请求
     */
    abortRequest() {
      const { requestTask } = this.data;
      if (requestTask?.abort) {
        try {
          // 移除所有监听器
          this._removeStreamListeners(requestTask);
          
          // 中止请求
          requestTask.abort();
          
          // 重置所有状态
          this.setData({
            currentText: '',
            pendingText: '',
            isStreaming: false,
            isTyping: false,
            isPaused: false,
            requestTask: null
          });

          // 停止打字效果
          this._stopTyping();
          
          // 触发中止事件
          this.triggerEvent('requestAborted');
          
          // 触发请求完成事件
          this.triggerEvent('requestComplete');
        } catch (error) {
          console.error('中止请求失败:', error);
          this._handleError('中止请求失败', error);
        }
      }
    },

    /**
     * 清空文本
     */
    clear() {
      this._cleanup();
      this.setData({
        currentText: '',
        pendingText: ''
      });
      this.triggerEvent('cleared');
    },

    /**
     * 暂停打字效果（仅打字机模式可用）
     */
    pause() {
      if (!this.properties.enableTypewriter) return;
      
      this._stopTyping();
      this.setData({ 
        isPaused: true,
        isTyping: false
      });
      this.triggerEvent('paused');
    },

    /**
     * 继续打字效果（仅打字机模式可用）
     */
    resume() {
      if (!this.properties.enableTypewriter) return;
      
      this.setData({ 
        isPaused: false,
        isTyping: false
      }, () => {
        // 在状态更新后开始打字
        this._startTyping();
        this.triggerEvent('resumed');
      });
    },

    /**
     * 立即完成打字（仅打字机模式可用）
     */
    complete() {
      if (!this.properties.enableTypewriter) return;
      
      this._stopTyping();
      this.setData({
        currentText: this.data.currentText + this.data.pendingText,
        pendingText: '',
        isTyping: false,
        isPaused: false
      });
      this.triggerEvent('finished');
    },

    /**
     * 处理接收到的文本
     * @private
     */
    _handleText(text) {
      if (!text) return;

      if (this.properties.enableTypewriter) {
        // 打字机模式：将文本加入队列
        this.setData({
          pendingText: this.data.pendingText + text
        }, () => {
          // 在文本更新后，如果没有在打字且没有暂停，则开始打字
          if (!this.data.isTyping && !this.data.isPaused) {
            this._startTyping();
          }
        });
      } else {
        // 直接显示模式：立即显示文本
        this.setData({
          currentText: this.data.currentText + text
        });
        this.triggerEvent('progress', {
          currentText: this.data.currentText,
          remainingLength: 0
        });
      }
    },

    /**
     * 开始打字效果
     * @private
     */
    _startTyping() {
      if (this.data.isTyping || this.data.isPaused || !this.properties.enableTypewriter) {
        return;
      }

      this.setData({ isTyping: true }, () => {
        this._typeNext();
      });
    },

    /**
     * 执行下一个字符的打字效果
     * @private
     */
    _typeNext() {
      if (!this.data.isTyping || this.data.isPaused) {
        return;
      }

      if (!this.data.pendingText.length) {
        this._stopTyping();
        this.triggerEvent('finished');
        return;
      }

      const nextChar = this.data.pendingText[0];
      const remainingText = this.data.pendingText.slice(1);
      
      this.setData({
        currentText: this.data.currentText + nextChar,
        pendingText: remainingText
      });

      this.triggerEvent('progress', {
        currentText: this.data.currentText,
        remainingLength: remainingText.length
      });

      // 设置下一个字符的定时器
      this.data.timer = setTimeout(() => {
        this._typeNext();
      }, this.properties.speed);
    },

    /**
     * 停止打字效果
     * @private
     */
    _stopTyping() {
      if (this.data.timer) {
        clearTimeout(this.data.timer);
        this.data.timer = null;
      }
      this.setData({ isTyping: false });
    },

    /**
     * 设置数据流监听器
     * @private
     */
    _setupStreamListeners(requestTask) {
      // 保存监听器引用，以便后续可以移除
      this._headerReceivedListener = headers => {
        this.triggerEvent('headersReceived', headers);
      };

      this._chunkReceivedListener = response => {
        try {
          if (!response?.data) return;
          if (!this.data.isStreaming) return; // 如果已经不是流式状态，不处理数据

          const text = this.data.decoder.decode(response.data);
          this._handleText(text);
          
          this.triggerEvent('chunkReceived', { 
            rawData: response.data,
            decodedText: text 
          });
        } catch (error) {
          this._handleError('处理数据块失败', error);
        }
      };

      // 设置监听器
      requestTask.onHeadersReceived?.(this._headerReceivedListener);
      requestTask.onChunkReceived(this._chunkReceivedListener);
    },

    /**
     * 移除数据流监听器
     * @private
     */
    _removeStreamListeners(requestTask) {
      if (!requestTask) return;
      
      // 移除监听器
      if (this._headerReceivedListener) {
        requestTask.offHeadersReceived?.(this._headerReceivedListener);
        this._headerReceivedListener = null;
      }

      if (this._chunkReceivedListener) {
        requestTask.offChunkReceived?.(this._chunkReceivedListener);
        this._chunkReceivedListener = null;
      }
    },

    /**
     * 处理请求成功
     * @private
     */
    _handleRequestSuccess(res) {
      this.triggerEvent('requestSuccess', res);
    },

    /**
     * 处理请求失败
     * @private
     */
    _handleRequestFail(error) {
      this._handleError('请求失败', error);
    },

    /**
     * 处理请求完成
     * @private
     */
    _handleRequestComplete() {
      this.setData({ 
        isStreaming: false,
        requestTask: null
      });
      this.triggerEvent('requestComplete');
    },

    /**
     * 处理错误
     * @private
     */
    _handleError(message, error) {
      console.error(message + ':', error);
      this.triggerEvent('error', { message, error });
      this._cleanup();
    },

    /**
     * 重置状态
     * @private
     */
    _resetState() {
      this._cleanup();
      this.setData({
        currentText: '',
        pendingText: '',
        isStreaming: false,
        isTyping: false,
        isPaused: false
      });
    },

    /**
     * 清理资源
     * @private
     */
    _cleanup() {
      this._stopTyping();
      const { requestTask } = this.data;
      if (requestTask) {
        this._removeStreamListeners(requestTask);
        if (requestTask.abort) {
          requestTask.abort();
        }
      }
      this.setData({
        isStreaming: false,
        requestTask: null,
        isPaused: false,
        isTyping: false
      });
    }
  }
}) 