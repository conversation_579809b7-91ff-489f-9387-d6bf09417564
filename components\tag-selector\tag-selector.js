Component({
  properties: {
    options: {
      type: Array,
      value: [],
      observer: function(newVal) {
        if (!Array.isArray(newVal)) {
          console.error('Invalid options:', newVal)
          this.setData({ options: [] })
        }
      }
    },
    selected: {
      type: Array,
      value: [],
      observer: function(newVal) {
        if (!Array.isArray(newVal)) {
          console.error('Invalid selected:', newVal)
          this.setData({ selected: [] })
        }
        // 更新选中状态映射
        this.updateSelectedMap(newVal)
      }
    }
  },

  data: {
    selectedMap: {} // 用于WXML快速查找
  },

  methods: {
    updateSelectedMap(selected) {
      const map = {}
      selected.forEach(item => map[item] = true)
      this.setData({ selectedMap: map })
    },

    handleTagTap(e) {
      if (!Array.isArray(this.data.options)) {
        return console.error('options不是数组')
      }

      const value = e.currentTarget.dataset.value
      const selected = [...this.data.selected]
      const index = selected.indexOf(value)

      if (index > -1) {
        selected.splice(index, 1)
      } else {
        selected.push(value)
      }

      this.setData({ selected })
      this.triggerEvent('change', { selected })
    }
  }
})