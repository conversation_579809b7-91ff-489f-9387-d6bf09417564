Component({
  properties: {
    // 最小值
    min: {
      type: Number,
      value: 0
    },
    // 最大值
    max: {
      type: Number,
      value: 100
    },
    // 最小值显示文本
    minText: {
      type: String,
      value: '最低'
    },
    // 最大值显示文本
    maxText: {
      type: String,
      value: '最高'
    },
    // 初始左值
    initialLeft: {
      type: Number,
      value: 20
    },
    // 初始右值
    initialRight: {
      type: Number,
      value: 80
    },
    // 步长
    step: {
      type: Number,
      value: 1
    },

    minGap: {  // 新增属性：最小间隔（单位与min/max相同）
      type: Number,
      value: 0  // 默认最小间隔为0（可以设置为正数确保有间隙）
    },

    // 新增属性：是否记住状态
    rememberState: {
      type: Boolean,
      value: false
    },

    // 新增属性：存储标识key（用于本地缓存）
    storageKey: {
      type: String,
      value: ''
    }

  },

  data: {
    leftValue: 0,
    rightValue: 0,
    leftPercent: 0,
    rightPercent: 0,
    sliderWidth: 0,
    startX: 0,
    currentThumb: null // 'left' or 'right'
  },

  lifetimes: {
    attached() {
      if (this.properties.rememberState && this.properties.storageKey) {
        // 尝试从缓存读取上次位置
        const savedState = wx.getStorageSync(this.properties.storageKey);
        if (savedState) {
          this.setData({
            leftValue: savedState.leftValue,
            rightValue: savedState.rightValue,
            leftPercent: this.calculatePercent(savedState.leftValue),
            rightPercent: this.calculatePercent(savedState.rightValue)
          });
          return;
        }
      }
      this.initSlider();
    }
  },

  methods: {    
    calculatePercent(value) {
      const { min, max } = this.properties;
      return ((value - min) / (max - min)) * 100;
    },
    
    calculateValue(percent) {
      const { min, max, step } = this.properties;
      const range = max - min;
      const value = min + (percent / 100) * range;
      // 根据步长调整值
      return Math.round(value / step) * step;
    },
    
    getSliderWidth() {
      const query = this.createSelectorQuery();
      query.select('.slider-area').boundingClientRect(rect => {
        if (rect) {
          this.setData({
            sliderWidth: rect.width
          });
        }
      }).exec();
    },
    
    // handleLeftTouchStart(e) {
    //   this.setData({
    //     currentThumb: 'left',
    //     startX: e.touches[0].clientX
    //   });
    // },    
    
    // handleRightTouchStart(e) {
    //   this.setData({
    //     currentThumb: 'right',
    //     startX: e.touches[0].clientX
    //   });
    // },

    handleLeftTouchStart(e) {
      this.setData({
        currentThumb: 'left',
        startX: e.touches[0].clientX
      });
      // 提前获取滑块尺寸
      this.createSelectorQuery()
        .select('.slider-area')
        .boundingClientRect()
        .exec();
    },
    
    handleRightTouchStart(e) {
      this.setData({
        currentThumb: 'right',
        startX: e.touches[0].clientX
      });
      // 提前获取滑块尺寸
      this.createSelectorQuery()
        .select('.slider-area')
        .boundingClientRect()
        .exec();
    },
    
    handleLeftTouchMove(e) {
      if (this.data.currentThumb !== 'left') return;
      this.handleThumbMove(e, 'left');
    },
    
    handleRightTouchMove(e) {
      if (this.data.currentThumb !== 'right') return;
      this.handleThumbMove(e, 'right');
    },
    
    // 修改移动处理逻辑
    // handleThumbMove(e, thumb) {
    //   const { sliderWidth } = this.data;
    //   const { min, max, step, minGap } = this.properties;
      
    //   const touchX = e.touches[0].clientX;
    //   const moveDistance = touchX - this.data.startX;
    //   const movePercent = (moveDistance / sliderWidth) * 100;

    //   // 计算新位置
    //   let newLeftPercent = this.data.leftPercent;
    //   let newRightPercent = this.data.rightPercent;

    //   if (thumb === 'left') {
    //     newLeftPercent = Math.max(0, this.data.leftPercent + movePercent);
    //     // 强制左滑块不超过右滑块减去最小间隔
    //     newLeftPercent = Math.min(newLeftPercent, this.data.rightPercent - (minGap / (max - min)) * 100);
    //   } else {
    //     newRightPercent = Math.min(100, this.data.rightPercent + movePercent);
    //     // 强制右滑块不低于左滑块加上最小间隔
    //     newRightPercent = Math.max(newRightPercent, this.data.leftPercent + (minGap / (max - min)) * 100);
    //   }

    //   // 更新数据
    //   this.setData({
    //     startX: touchX,
    //     leftPercent: newLeftPercent,
    //     rightPercent: newRightPercent,
    //     leftValue: this.calculateValue(newLeftPercent),
    //     rightValue: this.calculateValue(newRightPercent)
    //   });

    //   this.triggerChange();
    // },

    handleThumbMove(e, thumb) {
      const { sliderWidth, leftPercent, rightPercent } = this.data;
      const { min, max, step, minGap } = this.properties;
      
      // 获取滑动条实际屏幕位置（修复跳转的关键）
      const query = this.createSelectorQuery();
      query.select('.slider-area').boundingClientRect(rect => {
        if (!rect) return;
        
        const sliderLeft = rect.left;
        const touchX = e.touches[0].clientX;
        
        // 计算精确百分比（基于绝对位置）
        let newPercent = ((touchX - sliderLeft) / rect.width) * 100;
        newPercent = Math.max(0, Math.min(100, newPercent));
    
        // 根据当前滑块处理边界
        if (thumb === 'left') {
          const maxLeft = rightPercent - (minGap / (max - min)) * 100;
          newPercent = Math.min(newPercent, maxLeft);
        } else {
          const minRight = leftPercent + (minGap / (max - min)) * 100;
          newPercent = Math.max(newPercent, minRight);
        }
    
        // 计算新值（考虑步长）
        const newValue = this.calculateValue(newPercent);
        
        this.setData({
          [`${thumb}Percent`]: newPercent,
          [`${thumb}Value`]: newValue,
          startX: touchX // 更新起始位置
        });
    
        this.triggerChange();
      }).exec();
    },

    // 初始化时也检查重叠
    initSlider() {
      const { min, max, initialLeft, initialRight, minGap } = this.properties;
      
      // 确保初始值合法
      let leftVal = Math.max(min, Math.min(initialLeft, max - minGap));
      let rightVal = Math.min(max, Math.max(initialRight, leftVal + minGap));
      
      this.setData({
        leftValue: leftVal,
        rightValue: rightVal,
        leftPercent: this.calculatePercent(leftVal),
        rightPercent: this.calculatePercent(rightVal)
      });
      
      // this.getSliderWidth();
    },
    
    handleReset() {
      
      if (this.properties.rememberState && this.properties.storageKey) {
        wx.removeStorageSync(this.properties.storageKey);
      }
      this.initSlider();


      const { initialLeft, initialRight } = this.properties;
      
      this.setData({
        leftValue: initialLeft,
        rightValue: initialRight,
        leftPercent: this.calculatePercent(initialLeft),
        rightPercent: this.calculatePercent(initialRight)
      });
      
      // 触发change事件
      this.triggerChange();
    },
    
    triggerChange() {
      const state = {
        leftValue: this.data.leftValue,
        rightValue: this.data.rightValue
      };
      
      // 如果需要记住状态且设置了key，则保存到缓存
      if (this.properties.rememberState && this.properties.storageKey) {
        wx.setStorageSync(this.properties.storageKey, state);
      }
      
      this.triggerEvent('change', state);
    },
  }
});
