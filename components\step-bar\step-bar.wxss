/**
 * 步骤条组件样式
 * 
 * 样式结构：
 * - step-bar-container: 整个组件容器
 *   - icons-row: 图标行
 *     - icon-item: 图标项
 *       - step-icon: 步骤图标
 *   - step-line: 步骤条
 *     - step-line-bg: 步骤条背景
 *     - little-man: 小人动画元素
 *       - little-man-image: 小人图片
 *     - step-node: 步骤节点
 *       - step-node-active: 激活的节点内部圆点
 *   - text-row: 文字行
 *     - text-item: 文字项
 */

/* 步骤条容器 */
.step-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 0 10rpx;
}

/* 图标行样式 */
.icons-row {
  width: 90%;
  position: relative;
  height: 62rpx;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

/* 图标项基础样式 */
.icon-item {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80rpx;
  height: 80rpx;
  transform: translateX(-50%);
  transition: all 0.3s;
}

/* 图标项状态样式 */
.icon-item.hover {
  opacity: 0.8;
  transform: translateX(-50%) scale(1.05);
}

/* 步骤图标 */
.step-icon {
  width: 60rpx;
  height: 60rpx;
}

/* 步骤条样式 */
.step-line {
  width: 90%;
  height: 30rpx;
  margin: 20rpx 0;
  position: relative;
}

/* 步骤条背景 */
.step-line-bg {
  width: 103%;
  height: 26rpx;
  background: #F8F7F7;
  border-radius: 12rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

/* 步骤条激活部分样式已移除 */

/* 小人动画样式 */
.little-man {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 60rpx;
  height: 60rpx;
  transition: left 1.5s ease-in-out;
  will-change: left, transform;
  pointer-events: none; /* 确保小人不会拦截点击事件 */
}

/* 小人动画效果 */
.little-man.animate {
  animation: running 0.3s infinite alternate;
}

/* 小人图片 */
.little-man-image {
  width: 100%;
  height: 100%;
  display: block;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
}

/* 奔跑动画关键帧 */
@keyframes running {
  0% { transform: translate(-50%, -50%) scale(1) rotate(0deg); }
  25% { transform: translate(-50%, -52%) scale(1.05) rotate(5deg); }
  50% { transform: translate(-50%, -50%) scale(1) rotate(0deg); }
  75% { transform: translate(-50%, -52%) scale(1.05) rotate(-5deg); }
  100% { transform: translate(-50%, -50%) scale(1) rotate(0deg); }
}

/* 步骤节点基础样式 */
.step-node {
  width: 30rpx;
  height: 30rpx;
  background: #D9D9D9;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  transition: all 0.3s;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 步骤节点点击效果 */
.step-node:active {
  transform: translate(-50%, -50%) scale(1.2);
  background-color: #aaa;
}

/* 激活的步骤节点 */
.step-node.active {
  width: 38rpx;
  height: 38rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 激活节点内部圆点 */
.step-node-active {
  width: 18rpx;
  height: 18rpx;
  background: #252525;
  border-radius: 50%;
}

/* 已完成的步骤节点 */
.step-node.finished {
  background-color: #333;
  border: 2rpx solid #333;
}

/* 文字行样式 */
.text-row {
  width: 90%;
  position: relative;
  height: 60rpx;
  margin-top: 10rpx;
}

/* 文字项基础样式 */
.text-item {
  position: absolute;
  transform: translateX(-50%);
  text-align: center;
  font-size: 28rpx;
  color: #999;
  transition: all 0.3s;
  width: 120rpx; /* 适应更长的文本 */
}

/* 文字项状态 */
.text-item.active {
  color: #333;
  font-weight: bold;
}

.text-item.finished {
  color: #666;
}

/* 响应式调整 - 在小屏幕上缩小间距 */
@media screen and (max-width: 375px) {
  .step-bar-container {
    padding: 0 5rpx;
  }
  
  .step-icon {
    width: 50rpx;
    height: 50rpx;
  }
  
  .text-item {
    font-size: 24rpx;
    width: 100rpx;
  }
}