Component({
  /**
   * 组件的属性列表
   */
  properties: {
    buttonImage: {
      type: String,
      value: '../../subpackages/main/image/user-profile/icon_arrow_right_circle.png'
    },
    buttonWidth: {
      type: String,
      value: '48rpx'
    },
    buttonHeight: {
      type: String,
      value: '48rpx'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleButtonClick() {
      this.triggerEvent('buttonclick');
    }
  }
}) 