.food-recognizer {
  width: 100%;
  margin: 20px 0;
}

.recognizer-container {
  width: 100%;
  height: 120px;
  background: linear-gradient(135deg, #7EB67D 0%, #6BA86F 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(126, 182, 125, 0.3);
}

.recognizer-container.recognizing {
  background: linear-gradient(135deg, #91C788 0%, #7EB67D 100%);
  transform: scale(0.98);
}

.recognizer-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.recognizer-container:active::before {
  opacity: 1;
  animation: shine 0.6s ease;
}

.icon-area {
  margin-right: 20px;
}

.camera-icon {
  width: 60px;
  height: 60px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.camera-icon.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.camera-body {
  width: 40px;
  height: 30px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.camera-body::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px 4px 0 0;
}

.camera-lens {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #333;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.camera-lens::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #666;
  border-radius: 50%;
}

.camera-flash {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  background: #FFD700;
  border-radius: 50%;
  opacity: 0.8;
}

.text-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.main-text {
  font-size: 18px;
  font-weight: bold;
  color: white;
  margin-bottom: 4px;
  line-height: 1.2;
}

.sub-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
}

.loading-area {
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  animation: loading 1.4s ease-in-out infinite;
}

.dot1 {
  animation-delay: 0s;
}

.dot2 {
  animation-delay: 0.2s;
}

.dot3 {
  animation-delay: 0.4s;
}

/* 动画定义 */
@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .recognizer-container {
    height: 100px;
    padding: 15px;
  }
  
  .camera-icon {
    width: 50px;
    height: 50px;
  }
  
  .camera-body {
    width: 35px;
    height: 25px;
  }
  
  .camera-lens {
    width: 16px;
    height: 16px;
  }
  
  .main-text {
    font-size: 16px;
  }
  
  .sub-text {
    font-size: 12px;
  }
}

/* 简约线框样式 */
.simple-container {
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  margin: 20px auto;
  overflow: hidden; /* 确保图片不会超出边框 */
}

.simple-container:active {
  transform: scale(0.95);
}

.simple-container.recognizing {
  background: rgba(126, 182, 125, 0.05);
}

.plus-icon {
  position: relative;
  width: 24rpx;
  height: 24rpx;
  transition: all 0.3s ease;
}

.plus-icon.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.plus-horizontal,
.plus-vertical {
  position: absolute;
  transition: all 0.3s ease;
}

.plus-horizontal {
  width: 24rpx;
  height: 3rpx;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.plus-vertical {
  width: 3rpx;
  height: 24rpx;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

/* 上传后的图片样式 */
.uploaded-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  object-fit: cover;
}

/* 重新上传遮罩 */
.reupload-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.simple-container:hover .reupload-mask,
.simple-container:active .reupload-mask {
  opacity: 1;
}

.reupload-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
  position: relative;
}

.camera-mini {
  width: 24rpx;
  height: 18rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4rpx;
  position: relative;
}

.camera-mini::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 8rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2rpx 2rpx 0 0;
}

.camera-mini::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background: #333;
  border-radius: 50%;
  border: 1rpx solid rgba(255, 255, 255, 0.9);
}

.reupload-text {
  font-size: 20rpx;
  color: white;
  text-align: center;
  line-height: 1.2;
}

.simple-loading-dots {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.simple-dot {
  width: 4rpx;
  height: 4rpx;
  background: #7EB67D;
  border-radius: 50%;
  animation: loading 1.4s ease-in-out infinite;
}

.simple-dot1 {
  animation-delay: 0s;
}

.simple-dot2 {
  animation-delay: 0.2s;
}

.simple-dot3 {
  animation-delay: 0.4s;
}

/* 禁用状态 */
.food-recognizer[disabled] .recognizer-container {
  opacity: 0.6;
  background: #ccc;
  cursor: not-allowed;
}

.food-recognizer[disabled] .recognizer-container:active {
  transform: none;
}

.food-recognizer[disabled] .recognizer-container::before {
  display: none;
}

.food-recognizer[disabled] .simple-container {
  opacity: 0.6;
  cursor: not-allowed;
}

.food-recognizer[disabled] .simple-container:active {
  transform: none;
}

.food-recognizer[disabled] .plus-horizontal,
.food-recognizer[disabled] .plus-vertical {
  background: #E5E5E5;
} 