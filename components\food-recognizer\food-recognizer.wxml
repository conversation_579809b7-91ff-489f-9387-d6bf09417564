<view class="food-recognizer {{customClass}}" bindtap="onRecognizeClick">
  <!-- 默认样式 -->
  <view class="recognizer-container {{isRecognizing ? 'recognizing' : ''}}" wx:if="{{styleType === 'default'}}">
    <!-- 图标区域 -->
    <view class="icon-area">
      <view class="camera-icon {{isRecognizing ? 'loading' : ''}}">
        <view class="camera-body"></view>
        <view class="camera-lens"></view>
        <view class="camera-flash"></view>
      </view>
    </view>
    
    <!-- 文字区域 -->
    <view class="text-area">
      <text class="main-text">{{isRecognizing ? '正在识别中...' : buttonText}}</text>
      <text class="sub-text">{{isRecognizing ? '请稍候' : '点击拍照或选择图片'}}</text>
    </view>
    
    <!-- 加载动画 -->
    <view class="loading-area" wx:if="{{isRecognizing}}">
      <view class="loading-dots">
        <view class="dot dot1"></view>
        <view class="dot dot2"></view>
        <view class="dot dot3"></view>
      </view>
    </view>
  </view>

  <!-- 简约线框样式 -->
  <view class="simple-container {{isRecognizing ? 'recognizing' : ''}}" 
        wx:if="{{styleType === 'simple'}}"
        style="width: {{frameWidth}}rpx; height: {{frameHeight}}rpx; border-width: {{borderWidth}}rpx; border-style: {{borderStyle}}; border-color: {{isRecognizing ? activeBorderColor : borderColor}}; border-radius: {{borderRadius}}rpx;">
    
    <!-- 已上传的图片 -->
    <image class="uploaded-image" 
           src="{{uploadedImageUrl}}" 
           mode="aspectFit"
           wx:if="{{uploadedImageUrl && !isRecognizing}}"
           style="width: {{frameWidth - borderWidth * 2}}rpx; height: {{frameHeight - borderWidth * 2}}rpx; border-radius: {{borderRadius - borderWidth}}rpx;">
    </image>
    
    <!-- 加号图标 (仅在没有图片时显示) -->
    <view class="plus-icon {{isRecognizing ? 'loading' : ''}}" wx:if="{{!uploadedImageUrl}}">
      <view class="plus-horizontal" style="background: {{isRecognizing ? activeBorderColor : borderColor}};"></view>
      <view class="plus-vertical" style="background: {{isRecognizing ? activeBorderColor : borderColor}};"></view>
    </view>
    
    <!-- 重新上传遮罩 (有图片时显示) -->
    <view class="reupload-mask" wx:if="{{uploadedImageUrl && !isRecognizing}}">
      <view class="reupload-icon">
        <view class="camera-mini"></view>
      </view>
      <text class="reupload-text">点击重新上传</text>
    </view>
    
    <!-- 加载动画 -->
    <view class="simple-loading-dots" wx:if="{{isRecognizing}}">
      <view class="simple-dot simple-dot1"></view>
      <view class="simple-dot simple-dot2"></view>
      <view class="simple-dot simple-dot3"></view>
    </view>
  </view>
</view> 