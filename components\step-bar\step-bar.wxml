<!--
  步骤条组件
  
  功能：展示步骤流程，支持两端对齐布局和小人动画效果
  
  属性说明：
  - steps: 步骤数据数组，格式为[{icon: '图标路径', text: '步骤文本'}, ...]
  - current-step: 当前步骤索引（从0开始）
  - clickable: 是否允许点击切换步骤
  - little-man-icon: 自定义小人图标路径
  - animation-duration: 动画时长（单位：毫秒）
  - auto-animate: 是否自动播放动画（当currentStep变化时）
  
  事件：
  - stepchange: 步骤切换事件，detail = {step: 当前步骤, prevStep: 上一步骤}
  - animationstart: 动画开始事件，detail = {fromStep: 起始步骤, toStep: 目标步骤}
  - animationend: 动画结束事件
-->
<view class="step-bar-container">
  <!-- 图标区域 -->
  <view class="icons-row">
    <view 
      wx:for="{{steps}}" 
      wx:key="index" 
      class="icon-item"
      style="left:{{iconPositions[index]}}">
      <image class="step-icon" src="{{item.icon}}" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 步骤条区域 -->
  <view class="step-line">
    <!-- 步骤条背景 -->
    <view class="step-line-bg"></view>

    <!-- 小人动画元素 - 只在动画中显示 -->
    <block wx:if="{{showLittleMan && isAnimating}}">
      <view class="little-man {{isAnimating ? 'animate' : ''}}" style="left:{{littleManLeft}}">
        <image class="little-man-image" src="{{littleManIcon}}" mode="aspectFit"></image>
      </view>
    </block>

    <!-- 步骤节点 -->
    <view 
      wx:for="{{steps}}" 
      wx:key="index"
      class="step-node {{currentStep === index ? 'active' : ''}}"
      style="left:{{nodePositions[index]}}"
      data-index="{{index}}" 
      catch:tap="onStepClick">
      <view class="step-node-active" wx:if="{{currentStep === index}}"></view>
    </view>
  </view>

  <!-- 文字状态区域 -->
  <view class="text-row">
    <view 
      wx:for="{{steps}}" 
      wx:key="index"
      class="text-item {{currentStep === index ? 'active' : ''}}"
      style="left:{{textPositions[index]}}">
      {{item.text}}
    </view>
  </view>
</view>