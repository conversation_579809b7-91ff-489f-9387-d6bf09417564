/* components/custom-header/custom-header.wxss */
/* 2030年未来科技头部设计 - 灰白冷色调主题 */
.custom-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 30rpx;
  /* 优雅灰白冷色调渐变 */
  background: 
    radial-gradient(ellipse at 20% 50%, rgba(200, 220, 240, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 50%, rgba(180, 200, 230, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, 
      rgba(15, 20, 35, 0.92) 0%,          /* 深灰蓝 */
      rgba(25, 35, 55, 0.90) 25%,         /* 石墨蓝 */
      rgba(35, 45, 65, 0.88) 50%,         /* 钢铁蓝 */
      rgba(30, 40, 60, 0.90) 75%,         /* 月光蓝 */
      rgba(10, 15, 25, 0.94) 100%         /* 深夜蓝 */
    );
  /* 柔和模糊穿透 */
  backdrop-filter: blur(20px) saturate(120%);
  -webkit-backdrop-filter: blur(20px) saturate(120%);
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  /* 灰白色边界 */
  border-bottom: 1rpx solid rgba(200, 220, 240, 0.2);
  /* 柔和灰白阴影 */
  box-shadow: 
    0 8rpx 32rpx rgba(100, 120, 150, 0.15),
    0 4rpx 16rpx rgba(200, 220, 240, 0.08),
    0 2rpx 8rpx rgba(150, 170, 200, 0.12),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.08),
    inset 0 -1rpx 0 rgba(200, 220, 240, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: fixed;
}

/* AI神经网络脉冲动画 - 灰白冷色调 */
.custom-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 15% 30%, rgba(200, 220, 240, 0.06) 0%, transparent 30%),
    radial-gradient(circle at 85% 70%, rgba(180, 200, 230, 0.05) 0%, transparent 30%),
    radial-gradient(circle at 50% 50%, rgba(160, 180, 220, 0.04) 0%, transparent 40%);
  animation: neuralPulse 4s ease-in-out infinite;
  pointer-events: none;
}

/* 数据流动画 - 灰白冷色调 */
.custom-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(200, 220, 240, 0.08) 25%,
    rgba(180, 200, 230, 0.12) 50%,
    rgba(200, 220, 240, 0.08) 75%,
    transparent 100%
  );
  animation: dataStream 6s linear infinite;
  pointer-events: none;
}

/* 神经网络脉冲动画 */
@keyframes neuralPulse {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  25% {
    opacity: 0.4;
    transform: scale(1.03);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.01);
  }
  75% {
    opacity: 0.5;
    transform: scale(1.04);
  }
}

/* 数据流动画 */
@keyframes dataStream {
  0% {
    left: -100%;
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.8;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* 心率律动动画 */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.header-left {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 3;
  animation: heartbeat 3s ease-in-out infinite;
}

.header-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  /* 灰白色玻璃效果 */
  background: 
    radial-gradient(circle at 30% 30%, rgba(200, 220, 240, 0.15) 0%, transparent 70%),
    rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  border: 1rpx solid rgba(200, 220, 240, 0.25);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 3;
  box-shadow: 
    0 4rpx 12rpx rgba(150, 170, 200, 0.12),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.15);
}

.header-btn:active {
  background: 
    radial-gradient(circle at 30% 30%, rgba(200, 220, 240, 0.25) 0%, transparent 70%),
    rgba(255, 255, 255, 0.15);
  transform: scale(0.95);
  box-shadow: 
    0 8rpx 24rpx rgba(120, 140, 180, 0.2),
    0 4rpx 12rpx rgba(200, 220, 240, 0.25);
}

.logo {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  /* 柔和灰白发光 */
  filter: 
    drop-shadow(0 0 6rpx rgba(200, 220, 240, 0.4))
    drop-shadow(0 2rpx 4rpx rgba(150, 170, 200, 0.3));
  opacity: 1;
  position: relative;
  z-index: 3;
  animation: aiGlow 2s ease-in-out infinite alternate;
}

/* AI发光动画 - 灰白冷色调 */
@keyframes aiGlow {
  0% {
    filter: 
      drop-shadow(0 0 6rpx rgba(200, 220, 240, 0.4))
      drop-shadow(0 2rpx 4rpx rgba(150, 170, 200, 0.3));
  }
  100% {
    filter: 
      drop-shadow(0 0 8rpx rgba(200, 220, 240, 0.6))
      drop-shadow(0 2rpx 6rpx rgba(150, 170, 200, 0.4));
  }
}

.title {
  font-size: 36rpx;
  font-weight: 700;
  /* 优雅灰白色文字 */
  color: #f8f9fa;
  text-shadow: 
    0 0 8rpx rgba(200, 220, 240, 0.3),
    0 2rpx 4rpx rgba(150, 170, 200, 0.2);
  letter-spacing: 2rpx;
  position: relative;
  z-index: 3;
  animation: textShimmer 3s ease-in-out infinite;
}

/* 文字闪烁动画 - 灰白冷色调 */
@keyframes textShimmer {
  0%, 100% {
    text-shadow: 
      0 0 8rpx rgba(200, 220, 240, 0.3),
      0 2rpx 4rpx rgba(150, 170, 200, 0.2);
  }
  50% {
    text-shadow: 
      0 0 12rpx rgba(200, 220, 240, 0.5),
      0 2rpx 6rpx rgba(150, 170, 200, 0.3),
      0 0 16rpx rgba(180, 200, 230, 0.2);
  }
} 