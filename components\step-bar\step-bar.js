/**
 * 步骤条组件
 * 
 * 功能：
 * 1. 展示步骤流程，支持两端对齐布局
 * 2. 支持小人动画效果，从一个步骤移动到另一个步骤
 * 3. 支持自定义步骤图标和小人图标
 * 4. 支持点击步骤切换
 * 5. 支持外部控制当前步骤
 * 
 * 使用方法：
 * <step-bar 
 *   steps="{{steps}}" 
 *   current-step="{{currentStep}}" 
 *   clickable="{{true}}"
 *   little-man-icon="/path/to/icon.png"
 *   bind:stepchange="onStepChange"
 * />
 * 
 * 其中steps数组格式为：
 * [
 *   { icon: '/path/to/icon1.png', text: '步骤1' },
 *   { icon: '/path/to/icon2.png', text: '步骤2' },
 *   ...
 * ]
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 步骤数据数组
     * 格式：[{icon: '图标路径', text: '步骤文本'}, ...]
     */
    steps: {
      type: Array,
      value: []
    },
    
    /**
     * 当前步骤索引（从0开始）
     */
    currentStep: {
      type: Number,
      value: 0
    },
    
    /**
     * 是否允许点击切换步骤
     */
    clickable: {
      type: Boolean,
      value: true
    },
    
    /**
     * 自定义小人图标路径
     */
    littleManIcon: {
      type: String,
      value: '/subpackages/main/image/user-profile/icon_activity_very_active.png'
    },
    
    /**
     * 动画时长（单位：毫秒）
     */
    animationDuration: {
      type: Number,
      value: 1500
    },
    
    /**
     * 是否自动播放动画（当currentStep变化时）
     */
    autoAnimate: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isAnimating: false,       // 控制小人动画
    showLittleMan: false,     // 控制小人显示
    prevStep: 0,              // 记录上一个步骤位置
    nodePositions: [],        // 节点位置数组
    textPositions: [],        // 文本位置数组
    iconPositions: [],        // 图标位置数组
    littleManLeft: '0%',      // 小人位置
    animationStartTimerId: null, // 存储动画开始定时器ID
    animationEndTimerId: null,   // 存储动画结束定时器ID
    lastCurrentStep: 0,       // 上一次的currentStep值
    internalUpdate: false,    // 标记是否是内部更新，避免与外部更新冲突
    initialized: false        // 标记组件是否已初始化
  },
  
  /**
   * 数据监听器
   */
  observers: {
    /**
     * 监听currentStep和steps变化
     */
    'currentStep, steps': function(currentStep, steps) {
      if (!steps || steps.length === 0) return;
      
      // 只有在初始化或外部设置currentStep时更新位置
      if (!this.data.isAnimating) {
        const { lastCurrentStep } = this.data;
        
        // 初始化小人位置
        if (!this.data.initialized) {
          this.setData({
            prevStep: currentStep,
            initialized: true
          });
        }
        
        // 检查是否需要自动播放动画
        if (!this.data.internalUpdate && 
            this.properties.autoAnimate && 
            lastCurrentStep !== currentStep && 
            this.data.initialized) {
          // 执行动画
          this.runAnimation(lastCurrentStep, currentStep);
        }
        
        // 更新所有位置信息
        this.updatePositions();
        
        // 记录当前步骤，用于下次比较
        if (lastCurrentStep !== currentStep) {
          this.setData({
            lastCurrentStep: currentStep,
            internalUpdate: false // 重置内部更新标记
          });
        }
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算元素位置百分比 - 实现两端对齐
     * @param {Number} index - 当前索引
     * @param {Number} count - 总数量
     * @return {String} - 百分比位置
     */
    calculatePositionPercent(index, count) {
      // 如果只有一个元素，则居中显示
      if (count === 1) {
        return '50%';
      }
      
      // 实现两端对齐
      return (index / (count - 1) * 100) + '%';
    },
    
    /**
     * 更新所有元素的位置
     */
    updatePositions() {
      const { steps, prevStep } = this.data;
      
      if (!steps || steps.length === 0) return;
      
      const count = steps.length;
      
      // 计算位置数组
      const nodePositions = Array.from(
        { length: count }, 
        (_, i) => this.calculatePositionPercent(i, count)
      );
      
      // 设置小人位置
      const littleManLeft = this.calculatePositionPercent(prevStep, count);
      
      this.setData({
        nodePositions,
        textPositions: [...nodePositions],
        iconPositions: [...nodePositions],
        littleManLeft
      });
    },
    
    /**
     * 清除所有动画相关的定时器
     */
    clearAnimationTimers() {
      if (this.data.animationStartTimerId) {
        clearTimeout(this.data.animationStartTimerId);
      }
      
      if (this.data.animationEndTimerId) {
        clearTimeout(this.data.animationEndTimerId);
      }
      
      return { 
        animationStartTimerId: null,
        animationEndTimerId: null
      };
    },
    
    /**
     * 重置动画状态
     */
    resetAnimationState() {
      const timerState = this.clearAnimationTimers();
      
      this.setData({
        isAnimating: false,
        showLittleMan: false,
        ...timerState
      });
      
      // 触发动画结束事件
      this.triggerEvent('animationend', {});
    },
    
    /**
     * 小人跑步动画
     * @param {Number} fromStep - 起始步骤
     * @param {Number} toStep - 目标步骤
     * @returns {Boolean} 是否成功启动动画
     */
    runAnimation(fromStep, toStep) {
      // 清除可能存在的定时器
      this.clearAnimationTimers();
      
      // 如果已经在动画中，先重置动画状态
      if (this.data.isAnimating) {
        this.resetAnimationState();
      }
      
      // 检查步骤是否有效
      if (typeof fromStep !== 'number' || typeof toStep !== 'number' || 
          fromStep < 0 || toStep < 0 || 
          fromStep >= this.data.steps.length || toStep >= this.data.steps.length) {
        console.warn('[step-bar] 无效的步骤索引:', fromStep, toStep);
        return false;
      }
      
      // 获取动画时长
      const animationDuration = this.properties.animationDuration || 1500;
      
      // 触发动画开始事件
      this.triggerEvent('animationstart', { fromStep, toStep });
      
      // 设置动画开始状态
      this.setData({
        prevStep: fromStep,
        isAnimating: true
      }, () => {
        // 更新小人位置到起点
        this.updatePositions();
        
        // 显示小人并开始动画
        const animationStartTimerId = setTimeout(() => {
          this.setData({
            showLittleMan: true
          }, () => {
            // 延迟一下再开始移动小人，确保小人已经显示
            setTimeout(() => {
              // 更新小人位置到终点
              this.setData({
                prevStep: toStep
              }, () => {
                this.updatePositions();
                
                // 动画结束后隐藏小人
                const animationEndTimerId = setTimeout(() => {
                  this.resetAnimationState();
                }, animationDuration + 100);
                
                this.setData({
                  animationEndTimerId
                });
              });
            }, 50);
          });
        }, 10);
        
        this.setData({
          animationStartTimerId
        });
      });
      
      return true;
    },
    
    /**
     * 点击步骤节点
     * @param {Object} e - 事件对象
     */
    onStepClick(e) {
      // 解析点击的索引
      const index = parseInt(e.currentTarget.dataset.index);
      if (isNaN(index)) {
        return;
      }
      
      // 如果点击的就是当前步骤，不需要动画
      if (index === this.data.currentStep) {
        return;
      }
      
      // 如果不可点击或正在动画中，不处理点击
      if (!this.properties.clickable || this.data.isAnimating) {
        return;
      }
      
      // 记录当前步骤用于动画
      const prevStep = this.data.currentStep;
      
      // 标记为内部更新，避免与外部更新冲突
      this.setData({
        internalUpdate: true,
        currentStep: index,
        lastCurrentStep: index
      }, () => {
        // 启动小人奔跑动画
        this.runAnimation(prevStep, index);
        
        // 触发步骤切换事件
        this.triggerEvent('stepchange', { 
          step: index,
          prevStep: prevStep
        });
      });
    },
    
    /**
     * 手动触发动画
     * @param {Number} fromStep - 起始步骤索引
     * @param {Number} toStep - 目标步骤索引
     * @returns {Boolean} 是否成功启动动画
     */
    playAnimation(fromStep, toStep) {
      // 如果没有提供参数，则使用默认值
      const from = fromStep !== undefined ? fromStep : this.data.prevStep;
      const to = toStep !== undefined ? toStep : this.data.currentStep;
      
      return this.runAnimation(from, to);
    },
    
    /**
     * 初始化组件
     */
    initializeComponent() {
      // 初始化小人位置为当前步骤
      this.setData({
        prevStep: this.data.currentStep,
        lastCurrentStep: this.data.currentStep,
        initialized: true
      });
      
      // 更新位置信息
      this.updatePositions();
    },
    
    /**
     * 获取当前组件状态
     * @returns {Object} 组件当前状态
     */
    getStatus() {
      return {
        currentStep: this.data.currentStep,
        prevStep: this.data.prevStep,
        isAnimating: this.data.isAnimating,
        stepsCount: this.data.steps.length
      };
    }
  },
  
  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件生命周期函数-在组件实例进入页面节点树时执行
     */
    attached() {
      // 确保初始状态下动画标志为false
      this.setData({
        isAnimating: false,
        showLittleMan: false,
        internalUpdate: false,
        initialized: false
      });
      
      this.initializeComponent();
    },
    
    /**
     * 组件生命周期函数-在组件实例被从页面节点树移除时执行
     */
    detached() {
      // 清除动画定时器
      this.clearAnimationTimers();
    }
  },
  
  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    /**
     * 页面显示时触发
     */
    show() {
      // 页面显示时，如果之前有动画被中断，确保重置状态
      if (this.data.isAnimating) {
        this.resetAnimationState();
      }
    },
    
    /**
     * 页面隐藏时触发
     */
    hide() {
      // 页面隐藏时，如果有动画在进行，暂停动画
      if (this.data.isAnimating) {
        this.resetAnimationState();
      }
    }
  }
}) 