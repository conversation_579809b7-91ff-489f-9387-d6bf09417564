# Food Recognizer Component 食品识别组件

增强版食品识别组件，支持自定义线框样式、图片上传后预览和重新上传功能。

## 功能特性

- ✅ 支持拍照或从相册选择图片
- ✅ 可自定义线框的宽度、高度、边框样式
- ✅ 上传成功后图片覆盖线框显示
- ✅ 支持点击已上传图片重新上传
- ✅ 加载状态动画效果
- ✅ 多种边框样式（实线、虚线、点线）

## 基本用法

```xml
<!-- 基本用法 -->
<food-recognizer 
  bindrecognizesuccess="onRecognizeSuccess"
  bindrecognizefail="onRecognizeFail">
</food-recognizer>
```

## 自定义线框样式

```xml
<!-- 自定义尺寸和边框样式 -->
<food-recognizer 
  frameWidth="150"
  frameHeight="150"
  borderWidth="3"
  borderStyle="dashed"
  borderColor="#FF6B6B"
  activeBorderColor="#4ECDC4"
  borderRadius="20"
  bindrecognizesuccess="onRecognizeSuccess">
</food-recognizer>
```

## 属性列表

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| buttonText | String | '拍照识别菜品' | 上传按钮文字 |
| styleType | String | 'simple' | 样式类型：'default' 或 'simple' |
| showLoading | Boolean | true | 是否显示加载状态 |
| disabled | Boolean | false | 是否禁用组件 |
| customClass | String | '' | 自定义样式类名 |

### 线框样式属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| frameWidth | Number | 108 | 线框宽度（rpx） |
| frameHeight | Number | 108 | 线框高度（rpx） |
| borderWidth | Number | 2 | 边框宽度（rpx） |
| borderStyle | String | 'solid' | 边框样式：'solid'(实线) / 'dashed'(虚线) / 'dotted'(点线) |
| borderColor | String | '#C4C4C4' | 默认边框颜色 |
| activeBorderColor | String | '#7EB67D' | 激活/识别时的边框颜色 |
| borderRadius | Number | 16 | 圆角大小（rpx） |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| recognizestart | 开始识别时触发 | { imagePath } |
| recognizesuccess | 识别成功时触发 | { result, imagePath, imageUrl } |
| recognizefail | 识别失败时触发 | { error, imagePath } |
| imagecleared | 清除图片时触发 | - |

## 使用示例

### 页面 JavaScript

```javascript
Page({
  data: {
    // ...
  },

  // 识别成功回调
  onRecognizeSuccess(e) {
    const { result, imagePath, imageUrl } = e.detail;
    console.log('识别成功：', result);
    console.log('本地图片路径：', imagePath);
    console.log('服务器图片URL：', imageUrl);
    
    // 处理识别结果
    this.setData({
      recognizeResult: result
    });
  },

  // 识别失败回调
  onRecognizeFail(e) {
    const { error } = e.detail;
    console.error('识别失败：', error);
    
    wx.showToast({
      title: '识别失败，请重试',
      icon: 'none'
    });
  },

  // 图片清除回调
  onImageCleared(e) {
    console.log('图片已清除');
    // 可以在这里重置相关数据
  }
});
```

### 页面 WXML

```xml
<!-- 默认样式 -->
<food-recognizer 
  bindrecognizesuccess="onRecognizeSuccess"
  bindrecognizefail="onRecognizeFail">
</food-recognizer>

<!-- 大尺寸虚线边框 -->
<food-recognizer 
  frameWidth="200"
  frameHeight="150"
  borderWidth="4"
  borderStyle="dashed"
  borderColor="#FF9800"
  activeBorderColor="#4CAF50"
  borderRadius="24"
  bindrecognizesuccess="onRecognizeSuccess">
</food-recognizer>

<!-- 圆形样式 -->
<food-recognizer 
  frameWidth="120"
  frameHeight="120"
  borderWidth="3"
  borderStyle="solid"
  borderColor="#2196F3"
  activeBorderColor="#FF4081"
  borderRadius="60"
  bindrecognizesuccess="onRecognizeSuccess">
</food-recognizer>
```

## 样式自定义

可以通过 `customClass` 属性添加自定义样式：

```xml
<food-recognizer 
  customClass="my-custom-recognizer"
  bindrecognizesuccess="onRecognizeSuccess">
</food-recognizer>
```

```css
.my-custom-recognizer {
  margin: 40rpx 0;
}

.my-custom-recognizer .simple-container {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
```

## 注意事项

1. 确保小程序已经获得相机和相册访问权限
2. 图片上传需要配置正确的服务器API地址
3. 组件会自动处理图片压缩，确保上传效率
4. 重新上传时会清除之前的识别结果
5. 建议在网络良好的环境下使用以获得最佳体验

## 更新日志

### v2.0.0
- ✅ 新增自定义线框尺寸和边框样式功能
- ✅ 新增图片上传后预览功能
- ✅ 新增点击图片重新上传功能
- ✅ 优化用户交互体验
- ✅ 完善组件文档和示例代码 